"""
@Time     :   2025/06/20 15:35:38
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email    :   <EMAIL>
@File     :   rewrite.py
@Software :   Cursor
"""

import argparse
import os


def rewrite_files_as_new(input_dir: str, output_dir: str, extensions: list[str]):
    """
    Reads files from an input directory, filters them by specified extensions,
    and writes them as new files to an output directory.
    This process ensures that the new files have fresh timestamps (creation and modification dates).

    Args:
        input_dir (str): The path to the source directory.
        output_dir (str): The path to the destination directory.
        extensions (list[str]): A list of file extensions to process (e.g., ['pdf', 'jpg']).
    """
    # 1. Ensure the output directory exists.
    try:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"Created output directory: {output_dir}")
    except OSError as e:
        print(f"Error: Could not create output directory {output_dir}. {e}")
        return

    # 2. Get and sort the list of files from the input directory.
    try:
        filenames = sorted(os.listdir(input_dir))
    except FileNotFoundError:
        print(f"Error: Input directory not found at '{input_dir}'.")
        return
    except Exception as e:
        print(f"Error: Could not read input directory '{input_dir}'. {e}")
        return

    # 3. Process each file.
    processed_count = 0
    skipped_count = 0
    for filename in filenames:
        # Check if the file has one of the desired extensions.
        if any(filename.lower().endswith(f".{ext.lower()}") for ext in extensions):
            input_path = os.path.join(input_dir, filename)
            output_path = os.path.join(output_dir, filename)

            # Ensure it is a file, not a directory.
            if os.path.isfile(input_path):
                try:
                    # Read the binary content from the source file.
                    with open(input_path, "rb") as f_in:
                        content = f_in.read()

                    # Write the binary content to the destination file.
                    # This creates a new file with new metadata.
                    with open(output_path, "wb") as f_out:
                        f_out.write(content)

                    print(f"Successfully rewrote: {filename}")
                    processed_count += 1
                except OSError as e:
                    print(f"Error processing file {filename}: {e}")
                    skipped_count += 1
            else:
                skipped_count += 1
        else:
            skipped_count += 1

    print("\n--- Processing Summary ---")
    print(f"Total files processed: {processed_count}")
    print(f"Total files skipped: {skipped_count}")
    print("--------------------------")


def main():
    """
    Main function to parse command-line arguments and initiate file processing.
    """
    parser = argparse.ArgumentParser(
        description="Rewrite files from an input directory to an output directory, resetting all metadata like timestamps.",
        formatter_class=argparse.RawTextHelpFormatter,
    )
    parser.add_argument(
        "-i",
        "--input",
        required=True,
        metavar="<input_directory>",
        help="Path to the input directory containing source files.",
    )
    parser.add_argument(
        "-o",
        "--output",
        required=True,
        metavar="<output_directory>",
        help="Path to the output directory where new files will be saved.",
    )
    parser.add_argument(
        "-e",
        "--extensions",
        required=True,
        metavar="<ext1,ext2,...>",
        help="Comma-separated list of file extensions to process (e.g., 'pdf,jpg,png').",
    )

    args = parser.parse_args()

    # Convert the comma-separated string of extensions to a list.
    ext_list = [ext.strip() for ext in args.extensions.split(",")]

    print("Starting file rewrite process...")
    print(f"Input directory: {args.input}")
    print(f"Output directory: {args.output}")
    print(f"Target extensions: {ext_list}\n")

    rewrite_files_as_new(args.input, args.output, ext_list)


if __name__ == "__main__":
    input_directory = r"C:\NeoX\temp"
    output_directory = r"C:\NeoX\in"
    target_extensions = ["pdf", "jpg"]

    print("--- Running with hardcoded parameters ---")
    print(f"Input directory: {input_directory}")
    print(f"Output directory: {output_directory}")
    print(f"Target extensions: {target_extensions}\n")

    rewrite_files_as_new(input_directory, output_directory, target_extensions)
