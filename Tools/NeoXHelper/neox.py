"""
@Date       :   2024-02-01
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   neox.py
@Software   :   PyCharm
"""

from pathlib import Path

import fire

from common.configuration import console
from common.context import GlobalContext
from common.mapping import ActionScope


class Template:
    """
    usage: neox [template|demo] [generate] [--path=/your/path/to/generate/templates]
    """

    def __init__(self, path):
        self._tmpl_dir = Path(path).resolve() if path is not None else None
        self.scope = ActionScope(None, path=self._tmpl_dir)

    def generate(self):
        self.scope.generate_templates()


class Web:
    """
    usage: neox [web] [...] [--file=/your/path/to/*.toml]
    """

    # TODO(kuno): To be implemented...


class Windows:
    """
    usage:
        neox [windows|win] [feed|install|uninstall|set] [--file=/your/path/to/*.toml]
        neox [windows|win] [get] [0 or dpi | 1 or resolution]
    """

    def __init__(self, file):
        self.scope = ActionScope(GlobalContext(file).config)

    def feed(self):
        self.scope.feed_prescriptions()

    def install(self):
        self.scope.install_pkgs()

    def uninstall(self):
        self.scope.uninstall_pkgs()

    def get(self, screen_type: int | str):
        self.scope.get_screen(screen_type)

    def set(self):
        self.scope.set_screen()

    def mkdirs(self):
        self.scope.make_dirs()


class Nsips:
    """
    usage: neox [nsips] [get] [--file=/your/path/to/*.toml]
    """

    def __init__(self, file):
        self.scope = ActionScope(GlobalContext(file).config)

    def get(self):
        self.scope.get_nsips_charts()


class Pipeline:
    """
    A CLI tools which dealing with some requirements on Windows | Web | NeoX production.

    Examples:
        example 1:
            neox
        example 2:
            neox -- help

        -> explanation:
            Notice: The format is '-- help' !!!
            example 1 == example 2.

        example 3:
            neox template generate --path=.
        example 4:
            neox demo generate --path=~/neox

        -> explanation:
            Generate config templates to the specified directory.
            template file name:
                1. template_config.toml
            Notice:
                1. Both absolute paths and relative paths can be executed with '--path';
                2. The path must be present when you use '--path'.

        example 5:
            neox nsips get --file=config.toml

        -> explanation:
            Generate charts and xlsx according to the settings in the configuration file.
            The result files will be generated into src_dir/result.

        example 6:
            neox win feed --file=./config.toml
        example 7:
            neox windows install --file=config.toml
        example 8:
            neox win uninstall --file=~/neox/config.toml
        example 9:
            neox win get dpi == neox win get 0
        example 10:
            neox windows get resolution == neox win get 1
        example 11:
            neox windows set --file=config.toml
        example 12:
            neox win mkdirs --file=config.toml

        -> explanation:
            Both absolute paths and relative paths can be executed with '--file' when you need to customize config file.

    """

    def __init__(self, file=None, path=None):
        self._file = file
        self._path = path
        self.nsips = Nsips(self._file)
        self.windows = self.win = Windows(self._file)
        self.template = self.demo = Template(self._path)


def main():
    try:
        fire.Fire(Pipeline)
    except Exception:
        console.print_exception(show_locals=True)


if __name__ == "__main__":
    main()
