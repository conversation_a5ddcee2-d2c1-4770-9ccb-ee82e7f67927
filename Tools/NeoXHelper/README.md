# NeoXHelper

一个处理 Windows、Web 和 NeoX 生产环境需求的现代化 CLI 工具。

## 特性

- 🚀 **现代化架构**: 使用最新的 Python 包管理标准
- 🛠️ **代码质量**: 集成 ruff、mypy、pytest 等现代开发工具
- 📦 **依赖管理**: 使用 uv 进行快速依赖管理
- 🔧 **多平台支持**: 支持 Windows、Linux、macOS
- ⚡ **高性能**: 优化的依赖配置和构建流程

## 系统要求

- Python 3.10-3.12（推荐 3.12）
- uv 包管理器（推荐）

> **注意**: 由于依赖包兼容性问题，暂不支持 Python 3.13。推荐使用 Python 3.12。

## 安装配置

### 使用 uv（推荐）

```bash
# 克隆项目并进入目录
cd Tools/NeoXHelper

# 安装所有依赖（包括开发依赖）
uv sync --all-extras

# 或者分别安装
uv sync                    # 安装基础依赖
uv sync --group dev        # 安装开发依赖
uv sync --group executable # 安装构建依赖
```

### 传统方式

```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows: .venv\Scripts\activate
# Linux/macOS: source .venv/bin/activate

# 安装依赖
pip install -e .[dev,executable]
```

## 开发工具

### 代码质量检查

```bash
# 代码格式化
uv run ruff format .

# 代码检查和自动修复
uv run ruff check . --fix

# 类型检查
uv run mypy .

# 运行测试
uv run pytest
```

### 构建可执行文件

```bash
# 安装构建依赖
uv sync --group executable

# 使用 PyInstaller 构建
uv run pyinstaller neox.spec

# 或使用自定义命令构建
uv run pyinstaller --clean --onedir --console \
  --add-data "./templates/template_config.toml:./templates" \
  --add-binary "./bin/SetDpi.exe:./bin" \
  --hidden-import common \
  --hidden-import scopes \
  neox.py
```

## 使用方法

### 基本命令

```bash
# 显示帮助信息
uv run neox
uv run neox -- help

# 注意：使用 '-- help' 格式
```

### 模板管理

```bash
# 生成配置模板到当前目录
uv run neox template generate --path=.

# 生成配置模板到指定目录
uv run neox demo generate --path=~/neox
```

**说明：**
- 生成的模板文件名：`template_config.toml`
- `--path` 支持绝对路径和相对路径
- 目标路径必须存在

### NSIPS 功能

```bash
# 根据配置文件生成图表和 Excel 文件
uv run neox nsips get --file=config.toml
```

**说明：**
- 根据配置文件设置生成图表和 xlsx 文件
- 结果文件将生成到 `src_dir/result` 目录中

### Windows 系统管理

```bash
# 文件监控和同步
uv run neox win feed --file=./config.toml

# 软件安装
uv run neox windows install --file=config.toml

# 软件卸载
uv run neox win uninstall --file=~/neox/config.toml

# 获取系统信息
uv run neox win get dpi                    # 获取 DPI 设置
uv run neox win get 0                      # 等同于上面的命令
uv run neox windows get resolution         # 获取分辨率
uv run neox win get 1                      # 等同于上面的命令

# 系统设置
uv run neox windows set --file=config.toml

# 创建目录
uv run neox win mkdirs --file=config.toml
```

**说明：**
- `--file` 参数支持绝对路径和相对路径
- 配置文件格式为 TOML

## 配置文件

项目使用 TOML 格式的配置文件。使用以下命令生成模板：

```bash
uv run neox template generate --path=.
```

## 贡献指南

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建 Pull Request

### 开发规范

- 使用 `ruff` 进行代码格式化和检查
- 使用 `mypy` 进行类型检查
- 编写测试用例并确保通过
- 遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范

## 许可证

MIT License。

## 更新日志

### v0.1.0
- ✨ 项目现代化改造
- 🔧 集成 ruff、mypy、pytest 等开发工具
- 📦 使用 uv 进行依赖管理
- 🚀 优化构建和部署流程
