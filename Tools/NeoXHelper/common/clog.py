"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   clog.py
@Software   :   PyCharm
"""

from pathlib import Path

from loguru._logger import Logger


def set_log(
    flag: bool,
    clog: type[Logger],
    log_level: str | None = "INFO",
    log_file: Path | None = Path.cwd(),
) -> type[Logger] | None:
    """
    日志配置
    :param flag: 日志开关
    :param clog: loguru 日志对象
    :param log_level: 日志级别
    :param log_file: 生成的日志文件路径
    :return: logger or None
    """
    log_file_path = log_file.joinpath("neox_{time:YYYY-MM-DD}.log")
    if flag:
        clog.remove(handler_id=None)
        clog.add(
            sink=str(log_file_path),
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {thread} | {message}",
            rotation="10MB",
            retention=4,
            backtrace=True,
            diagnose=True,
            encoding="utf-8",
        )
        return clog
    return None
