# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml --extra executable -o requirements.txt
aiofiles==24.1.0
    # via sanic
altgraph==0.17.4
    # via
    #   macholib
    #   pyinstaller
html5tagger==1.3.0
    # via
    #   sanic
    #   tracerite
httptools==0.6.4
    # via sanic
macholib==1.16.3
    # via pyinstaller
multidict==6.6.3
    # via sanic
numpy==1.26.4
    # via
    #   neoxblank (pyproject.toml)
    #   opencv-python-headless
opencv-python-headless==*********
    # via neoxblank (pyproject.toml)
packaging==25.0
    # via
    #   pyinstaller
    #   pyinstaller-hooks-contrib
pyinstaller==6.14.2
    # via neoxblank (pyproject.toml)
pyinstaller-hooks-contrib==2025.7
    # via pyinstaller
sanic==24.12.0
    # via neoxblank (pyproject.toml)
sanic-routing==23.12.0
    # via sanic
setuptools==80.9.0
    # via
    #   pyinstaller
    #   pyinstaller-hooks-contrib
    #   sanic
tracerite==1.1.3
    # via sanic
typing-extensions==4.14.1
    # via sanic
ujson==5.10.0
    # via sanic
uvloop==0.21.0
    # via sanic
websockets==15.0.1
    # via sanic
