# NeoXBlank

现代化的图像空白检测工具，专门用于检查处方等文档是否为空白页面。提供HTTP API和命令行接口。

## ✨ 特性

- 🖼️ 支持本地文件和URL图像处理
- 🌐 HTTP API服务器（基于Sanic）
- 💻 命令行接口
- 📦 支持打包为Linux二进制可执行文件
- 🔧 现代化Python包结构
- ⚡ 使用uv进行快速依赖管理
- 🧹 使用ruff进行代码格式化和检查

## 🚀 快速开始

### 环境要求

- Python 3.10+
- uv (推荐的包管理器)

### 安装uv

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# 或使用pip
pip install uv
```

### 项目设置

```bash
# 克隆或进入项目目录
cd Tools/NeoXBlank

# 使用uv创建虚拟环境
uv venv

# 使用uv安装依赖
uv sync

# 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows
```

## 📖 使用方法

### 1. HTTP API 服务器模式

```bash
# 启动服务器（默认端口6999）
uv run neoxblank server

# 自定义主机和端口
uv run neoxblank server --host 0.0.0.0 --port 8080

# 启用调试模式
uv run neoxblank server --debug
```

### 2. 命令行模式

```bash
# 直接检查图像
uv run neoxblank check /path/to/image.jpg

# 检查网络图像
uv run neoxblank check https://example.com/image.jpg
```

### 3. API 使用示例

#### 检查空白页面

```bash
curl -X POST http://localhost:6999/check_blank \
  -H "Content-Type: application/json" \
  -d '{"image_path": "/path/to/image.png"}'
```

#### 健康检查

```bash
curl http://localhost:6999/health
```

#### 响应格式

```json
{
    "result": 0.5
}
```

> **说明**: `result` 值越低表示图像越接近空白页面。

## 🔨 开发环境设置

### 安装开发依赖

```bash
# 安装所有依赖（包括开发工具）
uv sync --all-extras

# 或者只安装开发依赖
uv add --group dev pytest pytest-asyncio ruff mypy
```

### 代码格式化和检查

```bash
# 使用 ruff 进行代码格式化
uv run ruff format src/

# 使用 ruff 进行代码检查和自动修复
uv run ruff check src/ --fix

# 仅检查代码（不修复）
uv run ruff check src/

# 类型检查
uv run mypy src/

# 运行所有代码质量检查
uv run ruff check src/ && uv run ruff format src/ --check && uv run mypy src/
```

### 代码质量工具说明

本项目使用 **ruff** 作为主要的代码质量工具，它集成了以下功能：

- **代码格式化**: 替代 black，提供一致的代码风格
- **导入排序**: 替代 isort，自动整理导入语句
- **代码检查**: 替代 flake8，检测潜在问题和代码异味
- **性能优化**: ruff 比传统工具快 10-100 倍

**ruff 配置特点**：
- 兼容 black 的格式化风格
- 启用了多种检查规则（pycodestyle、pyflakes、bugbear等）
- 针对项目结构进行了优化配置

### 运行测试

```bash
# 运行所有测试
uv run pytest

# 运行特定测试
uv run pytest tests/test_image_processor.py -v
```

## 📦 构建Linux二进制可执行文件

### 安装构建依赖

```bash
# 安装PyInstaller和相关构建工具
uv add --group executable pyinstaller
```

### 构建二进制文件

```bash
# 使用构建脚本（推荐）
uv run python scripts/build.py

# 或手动构建
uv run pyinstaller neoxblank.spec --clean
```

构建完成后，二进制文件将位于 `dist/neoxblank`。

### 测试二进制文件

```bash
# 测试帮助信息
./dist/neoxblank --help

# 测试服务器启动
./dist/neoxblank server

# 测试图像检查
./dist/neoxblank check /path/to/test/image.jpg
```

## 🚀 部署

### 1. 开发环境部署

```bash
# 后台运行服务器
nohup uv run neoxblank server > neoxblank.log 2>&1 &

# 查看日志
tail -f neoxblank.log
```

### 2. 生产环境部署（二进制）

```bash
# 复制二进制文件到服务器
scp dist/neoxblank user@server:/usr/local/bin/

# 在服务器上运行
/usr/local/bin/neoxblank server --host 0.0.0.0 --port 6999

# 或使用systemd服务
sudo systemctl start neoxblank
sudo systemctl enable neoxblank
```

### 3. Docker部署（可选）

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install uv && uv sync --frozen

EXPOSE 6999

CMD ["uv", "run", "neoxblank", "server"]
```

## 🔧 配置选项

### 环境变量

- `NEOXBLANK_HOST`: 服务器绑定主机（默认: 0.0.0.0）
- `NEOXBLANK_PORT`: 服务器端口（默认: 6999）
- `NEOXBLANK_DEBUG`: 调试模式（默认: false）

### 命令行参数

```bash
# 查看所有可用选项
uv run neoxblank --help
uv run neoxblank server --help
uv run neoxblank check --help
```

## 📁 项目结构

```
NeoXBlank/
├── pyproject.toml          # 项目配置和依赖
├── README.md               # 项目文档
├── src/
│   └── neoxblank/
│       ├── __init__.py     # 包初始化
│       ├── core/
│       │   ├── __init__.py
│       │   └── image_processor.py  # 核心图像处理逻辑
│       ├── api/
│       │   ├── __init__.py
│       │   └── server.py   # HTTP API服务器
│       └── cli/
│           ├── __init__.py
│           └── main.py     # 命令行入口
├── scripts/
│   └── build.py           # 构建脚本
└── tests/                 # 测试文件（待添加）
```

## 🤝 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'feat: add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 🆘 故障排除

### 常见问题

1. **OpenCV安装问题**
   ```bash
   # 如果遇到OpenCV安装问题，尝试：
   uv add opencv-python-headless
   ```

2. **权限问题**
   ```bash
   # 确保二进制文件有执行权限
   chmod +x dist/neoxblank
   ```

3. **端口占用**
   ```bash
   # 检查端口使用情况
   lsof -i :6999

   # 使用不同端口
   uv run neoxblank server --port 8080
   ```

### 获取帮助

- 联系开发团队: <EMAIL>
