#!/usr/bin/env python3
"""
@Date       :   2024/12/11
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   test_rts_monitoring.py
@Software   :   PyCharm
"""

import random
import time
from pathlib import Path

import pendulum
import redis

from neox_metrics import NeoXMetrics


def create_test_data(metrics):
    """
    创建测试数据
    """
    print("正在创建测试数据...")

    # 连接到 Redis
    r = redis.StrictRedis(
        host=metrics.redis_host,
        port=metrics.redis_port,
        password=metrics.redis_password,
        decode_responses=True,
    )

    # 确保所有必要的 TimeSeries key 存在
    keys_to_create = [metrics.rts_src_key, metrics.rts_tar_key, metrics.rts_compare_key]
    for key in keys_to_create:
        try:
            # 检查 key 是否存在
            r.execute_command("TS.INFO", key)
            print(f"  TimeSeries key {key} 已存在")
        except Exception:
            # key 不存在，创建它
            try:
                r.execute_command("TS.CREATE", key)
                print(f"  成功创建 TimeSeries key: {key}")
            except Exception as e:
                print(f"  创建 TimeSeries key {key} 失败: {e}")
                return False

    # 获取当前日本时间
    now_jst = pendulum.now("Asia/Tokyo")

    # 为当前小时创建一些测试数据
    hour_start = now_jst.start_of("hour")

    # 在当前小时内创建10个数据点
    for i in range(10):
        timestamp = hour_start.add(minutes=i * 5)  # 每5分钟一个数据点
        value = random.randint(1, 100)  # 随机值
        ts_timestamp = int(timestamp.timestamp() * 1000)

        try:
            r.execute_command("TS.ADD", metrics.rts_src_key, ts_timestamp, value)
            print(f"  添加数据: {timestamp} -> {value}")
        except Exception as e:
            print(f"  添加数据失败: {e}")

    # 为上周同日同时间创建一些测试数据
    last_week_start = hour_start.subtract(weeks=1)

    # 在上周同时间段创建5个数据点
    for i in range(5):
        timestamp = last_week_start.add(minutes=i * 10)  # 每10分钟一个数据点
        value = random.randint(50, 150)  # 随机值
        ts_timestamp = int(timestamp.timestamp() * 1000)

        try:
            r.execute_command("TS.ADD", metrics.rts_tar_key, ts_timestamp, value)
            print(f"  添加上周数据: {timestamp} -> {value}")
        except Exception as e:
            print(f"  添加上周数据失败: {e}")

    print("测试数据创建完成！")


def test_rts_monitoring():
    """
    测试 RTS 监控功能
    """
    # 配置文件路径
    config_path = Path("metrics.toml")

    # 创建 NeoXMetrics 实例
    metrics = NeoXMetrics(config_path)

    print("开始测试 RTS 监控功能...")

    # 创建测试数据
    print("\n0. 创建测试数据:")
    create_test_data(metrics)

    # 测试单独的方法
    print("\n1. 测试获取当前小时数据之和:")
    hourly_sum = metrics.get_hourly_sum_from_start(metrics.rts_src_key)
    print(f"当前小时数据之和: {hourly_sum}")

    print("\n2. 测试写入数据到 Redis TimeSeries:")
    success = metrics.write_to_redis_ts(metrics.rts_tar_key, hourly_sum)
    print(f"写入结果: {'成功' if success else '失败'}")

    print("\n3. 测试获取上周同日同时间 Last 值:")
    last_week_value = metrics.get_last_week_same_hour_last_value(metrics.rts_tar_key)
    print(f"上周同日同时间 Last 值: {last_week_value}")

    print("\n4. 测试写入对比数据:")
    success = metrics.write_to_redis_ts(metrics.rts_compare_key, last_week_value)
    print(f"写入对比数据结果: {'成功' if success else '失败'}")

    print("\n5. 启动 RTS 监控任务:")
    metrics.start_rts_monitoring()

    # 运行一段时间来观察任务执行
    print("RTS 监控已启动，将运行 5 分钟进行测试...")
    try:
        time.sleep(300)  # 运行 5 分钟
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止...")

    # 停止监控
    metrics.stop_rts_monitoring()
    print("RTS 监控已停止")


def test_with_prometheus():
    """
    测试同时运行 RTS 监控和 Prometheus 指标
    """
    config_path = Path("metrics.toml")

    print("启动完整的监控系统（包括 RTS 和 Prometheus）...")

    # 使用 main 函数启动，enable_rts=True
    from neox_metrics import main

    main(config_path)


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--full":
        # 完整测试模式
        test_with_prometheus()
    else:
        # 单独测试 RTS 功能
        test_rts_monitoring()
