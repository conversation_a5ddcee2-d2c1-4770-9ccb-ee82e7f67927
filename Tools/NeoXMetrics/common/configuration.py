"""
@Date       :   2024-09-06
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   configuration.py
@Software   :   PyCharm
"""

import sys
from collections.abc import MutableMapping
from pathlib import Path
from typing import Any

if sys.version_info >= (3, 11):
    import tomllib
else:
    import tomli as tomllib


def load_toml(config_file: Path) -> MutableMapping[str, Any]:
    """
    读取 toml 配置文件
    :param config_file: toml 配置文件
    :return: 字典格式的配置文件内容
    """
    # 打开读取 toml 配置文件
    with open(config_file, "rb") as f:
        config = tomllib.load(f)
    return config
