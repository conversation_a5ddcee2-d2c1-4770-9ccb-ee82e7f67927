# NeoXMetrics

[![Python](https://img.shields.io/badge/Python-3.10%2B-blue.svg)](https://www.python.org/downloads/)
[![Code style: ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/astral-sh/ruff/main/assets/badge/v2.json)](https://github.com/astral-sh/ruff)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

一个现代化的 Prometheus 指标监控和 Redis TimeSeries 数据处理 CLI 工具。

## ✨ 功能特性

- **🔍 Prometheus 指标监控**：从 Redis TimeSeries 读取数据并暴露为 Prometheus 指标
- **📊 RTS 数据处理**：Redis TimeSeries 数据汇总、转存和对比功能
- **🌏 日本时区支持**：所有时间计算使用日本时区 (Asia/Tokyo)
- **⏰ 定时任务**：支持每分钟和每小时的自动化数据处理
- **🚀 现代化工具链**：使用 uv、ruff、pytest 等现代 Python 工具
- **📦 二进制打包**：支持 PyInstaller 构建独立可执行文件

## 📋 系统要求

- Python 3.10 或更高版本
- Redis 服务器（支持 TimeSeries 模块）
- 推荐使用 [uv](https://docs.astral.sh/uv/) 进行依赖管理

## 🚀 快速开始

### 使用 uv（推荐）

```bash
# 克隆项目并进入目录
cd Tools/NeoXMetrics

# 创建虚拟环境
uv venv

# 激活虚拟环境
source .venv/bin/activate

# 安装主要依赖
uv sync

# 或安装所有依赖（包括开发工具）
uv sync --all-extras

# 运行程序
uv run python neox_metrics.py

# 或者使用命令行入口
uv run neoxmetrics
```

### 传统方式

```bash
# 安装依赖
pip install -e .

# 运行程序
python neox_metrics.py
```

## 🔧 配置

1. **配置文件**：编辑 `metrics.toml` 文件以配置 Redis 连接、Prometheus 设置和监控指标
2. **启动服务**：
   ```bash
   # 开发模式
   uv run python neox_metrics.py

   # 生产模式（后台运行）
   nohup uv run python neox_metrics.py > neox_metrics.log 2>&1 &
   ```

3. **Prometheus 配置**：在 Prometheus 配置文件中添加抓取配置：
   ```yaml
   scrape_configs:
   - job_name: 'neox_metrics'
     static_configs:
       - targets: ['localhost:7333']
   ```

## 🛠️ 开发工具

### 代码质量检查

```bash
# 运行 linting 和格式化
uv run ruff check . --fix
uv run ruff format .

# 类型检查
uv run mypy .
```

### 测试

```bash
# 运行所有测试
uv run pytest

# 运行测试并生成覆盖率报告
uv run pytest --cov=common --cov=neox_metrics
```

### 构建可执行文件

```bash
# 安装构建依赖
uv sync --group executable

# 构建二进制文件
uv run pyinstaller --clean --onefile neox_metrics.py
```

# RTS 监控功能

NeoXMetrics 现在支持 Redis TimeSeries (RTS) 数据处理功能，包括：

- **每分钟数据汇总**：从源 key 查询当前小时的数据之和并写入目标 key
- **每小时对比数据**：查询上周同日同时间的数据并写入对比 key
- **日本时区支持**：所有时间计算使用 Asia/Tokyo 时区

详细使用说明请参考：**[RTS 监控功能文档](RTS_MONITORING_README.md)**

## RTS 监控快速开始

1. **创建必要的 TimeSeries 键**：
   ```bash
   redis-cli
   TS.CREATE GRAFANA_NEOX_ENGINE_REQUEST_COUNT
   TS.CREATE GRAFANA_SDE_PRES_COUNT_TODAY
   TS.CREATE GRAFANA_SDE_PRES_COUNT_LAST_WEEKDAY
   ```

2. **测试连接**：
   ```bash
   python test_redis_connection.py
   ```

3. **启动监控**：
   ```bash
   # 启动完整监控（包括 RTS 和 Prometheus）
   python neox_metrics.py

   # 或仅测试 RTS 功能
   python test_rts_monitoring.py
   ```

# 文档

- **[RTS 监控功能详细文档](RTS_MONITORING_README.md)** - Redis TimeSeries 数据处理功能的完整说明
- **[配置文件说明](metrics.toml)** - 配置参数详解
- **[测试工具](test_redis_connection.py)** - Redis 连接和 TimeSeries 模块测试

## 📁 项目结构

```
Tools/NeoXMetrics/
├── neox_metrics.py              # 主程序入口
├── pyproject.toml               # 现代化项目配置
├── metrics.toml                 # 业务配置文件
├── test_rts_monitoring.py       # RTS 功能测试
├── test_redis_connection.py     # Redis 连接测试
├── test_config.py               # 配置测试
├── README.md                    # 主文档
├── RTS_MONITORING_README.md     # RTS 功能详细文档
├── neox_metrics.spec           # PyInstaller 配置
├── neox_metrics.sh             # 启动脚本
├── .python-version             # Python 版本配置
└── common/
    └── configuration.py         # 配置文件加载工具
```

## 🤝 贡献指南

1. **代码风格**：使用 ruff 进行代码格式化和 linting
2. **类型注解**：使用 mypy 进行静态类型检查
3. **测试**：为新功能编写测试用例
4. **文档**：更新相关文档

### 开发流程

```bash
# 1. 安装开发环境
uv sync --all-extras

# 2. 进行开发
# ... 编写代码 ...

# 3. 运行质量检查
uv run ruff check . --fix
uv run ruff format .
uv run mypy .

# 4. 运行测试
uv run pytest

# 5. 提交代码
git add .
git commit -m "feat: add new feature"
```

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 🔗 相关链接

- **[RTS 监控功能详细文档](RTS_MONITORING_README.md)** - Redis TimeSeries 数据处理功能的完整说明
- **[配置文件说明](metrics.toml)** - 配置参数详解
- **[测试工具](test_redis_connection.py)** - Redis 连接和 TimeSeries 模块测试
