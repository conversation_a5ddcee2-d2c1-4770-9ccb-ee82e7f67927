#!/usr/bin/env python3

import toml


def test_config():
    """测试配置文件解析"""
    try:
        with open("metrics.toml", encoding="utf-8") as f:
            config = toml.load(f)

        print("✅ 配置文件解析成功！")

        # 测试 sliding_window_sizes
        sliding_window_sizes = [
            gauge["sliding_window_sec"] for gauge in config["metrics"]["gauge"]
        ]
        print(f"✅ sliding_window_sizes: {sliding_window_sizes}")

        # 测试 redis_ts_keys - 使用新的共享配置结构
        shared_ts_keys = config["metrics"]["redis"]["ts_keys"]
        redis_ts_keys = [shared_ts_keys for _ in config["metrics"]["gauge"]]
        print(f"✅ redis_ts_keys 长度: {len(redis_ts_keys)}")
        print(f"✅ 共享的 ts_keys: {shared_ts_keys[:2]}...")  # 只显示前两个

        print("✅ 所有配置解析正常！")

    except Exception as e:
        print(f"❌ 配置文件解析错误: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    test_config()
