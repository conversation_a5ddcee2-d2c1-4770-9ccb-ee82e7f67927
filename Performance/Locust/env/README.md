# 项目设置和工作流程

本文档提供了设置项目环境、构建本地包和部署应用程序的说明。本项目使用 [uv](https://github.com/astral-sh/uv) 进行包和虚拟环境管理。

`pyproject.toml` 定义了项目依赖，`uv.toml` 包含 `uv` 的特定配置，如 Python 解释器和 PyPI 索引 URL。

## 1. 环境设置（使用 uv）

### 1.1. 安装 uv

开始之前，您需要安装 `uv`。根据您的操作系统运行相应命令：

**macOS & Linux:**
```shell
curl -Lfs https://astral.sh/uv/install.sh | sh
```

**Windows:**
```shell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```
更多安装选项，请参阅 [官方 uv 文档](https://docs.astral.sh/uv/installing/)。

### 1.2. 设置步骤

在当前目录（`Performance/Locust/env/`）中执行以下步骤。

**步骤 1：创建虚拟环境**

`uv` 将自动发现当前目录中的 `uv.toml` 文件，并读取其中的配置。

```bash
# 在 env 目录下执行
cd Performance/Locust/env

# 创建虚拟环境并给虚拟环境命名
uv venv .locust-venv

# 或者指定 python 版本创建虚拟环境（前提是本地存在的版本）并给虚拟环境命名
uv venv --python 3.13 .locust-venv

# 或者指定 python 解释器创建虚拟环境并给虚拟环境命名
uv venv --python ${HOME}/miniconda3/envs/python3.13/bin/python .locust-venv
```

**步骤 2：激活虚拟环境**

**macOS & Linux:**
```bash
source .locust-venv/bin/activate
```

**Windows:**
```powershell
.locust-venv\Scripts\Activate.ps1
```

**步骤 3：安装依赖**

项目依赖分为 `dev`（用于开发）和 `latest`（用于 CI/生产）。根据需要选择其中一个。

*   **安装开发依赖：**
    这将安装核心依赖、开发额外工具（如 `pipdeptree`）和可编辑模式的本地包。
    ```bash
    cd requirements
    uv pip install --python $(which python) -r requirements.dev.txt
    ```

*   **安装 `latest`（CI）依赖：**
    这将从预构建的 `.whl` 文件安装核心依赖和本地包，适用于 CI/CD 环境。
    ```bash
    cd requirements
    uv pip install --python $(which python) -r requirements.latest.txt
    ```

*   **验证依赖是否按预期安装：**
    不要直接使用 `pip list`，因为它可能会显示系统预装的包。
    
    请使用 `uv pip list` 验证命令如下：
    ```bash
    uv pip list --python $(which python)
    ```

完成这些步骤后，您的环境就准备好了。

## 2. 构建本地包的 Wheels

本节概述了如何为本地库（`locust_common`、`neox_locust`）构建 `.whl` 文件。有关不同构建方法的更详细说明，请参阅 [构建指南](../../../lib/Build.md)。

1.  **构建包**：导航到目标库的源目录（例如 `lib/locust_common`）并运行 `hatch build`。这将在该库的 `dist/` 子目录中生成一个 wheel 文件。
    ```bash
    # 构建 locust_common 包的示例
    cd ../../../lib/locust_common
    hatch build
    ```

2.  **移动 wheel 文件**：构建完成后，将新的 wheel 从库的 `dist/` 文件夹移动到此环境的本地 `lib/` 目录（`Performance/Locust/env/lib/`）。
    ```bash
    # 示例：从 'Performance/Locust/env' 目录运行
    mv ../../../lib/locust_common/dist/*.whl ./lib/
    ```

3.  **更新配置**：
    *   如果在 `./.env` 文件中跟踪了新版本，请更新该文件。
    *   更新此项目的 `pyproject.toml` 中 `[project.optional-dependencies.latest]` 下的包引用，以匹配新 wheel 文件的名称和版本。

## 3. 部署

### 阶段 1：构建 Docker 镜像

1.  编辑 `.env` 文件。
2.  确认 `.env` 中的所有变量都是正确的。
3.  检查 `Dockerfile`。
4.  运行 `bash BuildImage.sh` 来构建自定义的 Locust Docker 镜像。

### 阶段 2：运行负载测试

1.  检查 `docker-compose.yml` 和 `.env`。
2.  运行 `docker-compose up -d` 来启动负载测试。
3.  用于调试，检查日志：
    *   `${VOLUMES_PATH}/scenerios/\*/\*/run.log`
    *   `${VOLUMES_PATH}/scenerios/\*/\*/internal_*.log`