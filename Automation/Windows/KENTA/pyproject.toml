# KENTA Windows UI Automation Testing Project
# This is an application project for KENTA UI automation testing

[project]
# project meta
name = "kenta-automation"
version = "1.0.0"
requires-python = ">=3.11"
dependencies = [
    # Testing framework dependencies
    "pytest==8.3.5",
    "pytest-html==4.1.1",
    "pytest-order==1.3.0",
    "pytest-repeat==0.9.4",
    "pytest-rerunfailures==15.1",
    "pytest-timeout==2.4.0",
    "pytest-xdist==3.7.0",
    "allure-pytest==2.14.2",
    "pip-autoremove",
    "pipdeptree",
]

[project.optional-dependencies]
kenta = [
    # Windows UI automation dependencies (KENTA specific)
    "jmespath==1.0.1",
    "playwright==1.52.0",
    "pytest-playwright==0.7.0",
    "PyAutoGUI==0.9.54",
    "pillow==11.2.1",
    "uiautomation==2.0.28",
]

local = [
    # Local editable packages (shared libraries)
    "neox_test_common",
    "neox_test_win",
    "neox_test_scenarios",
]

dev-tools = [
    # Development tools
    "ruff",       # 现代化 linter + formatter
    "mypy",       # 类型检查
    "pre-commit", # Git 钩子管理
    "pytest-cov", # 测试覆盖率
    "bandit",     # 安全检查
]

dev = [
    "kenta-automation[kenta]",
    "kenta-automation[local]",
    "kenta-automation[dev-tools]",
]

[tool.uv.pip]
# The URL of the Python package index (by default: https://pypi.org/simple).
# Set the package registry index URL.
# Using a regional mirror can speed up dependency installation.
index-url = "https://mirrors.aliyun.com/pypi/simple"

# The minimum Python version that should be supported by the resolved requirements.
python-version = "3.11"

# Set the Python interpreter for this project.
# uv will use this interpreter to create virtual environments.
python = "C:\\miniconda3\\envs\\python3.13"

[tool.uv.sources]
# Local package sources for uv
neox_test_common = { path = "../../../lib/neox_test_common", editable = true }
neox_test_win = { path = "../../../lib/neox_test_win", editable = true }
neox_test_scenarios = { path = "../../../lib/neox_test_scenarios", editable = true }

[tool.pytest.ini_options]
# Pytest configuration for KENTA project
log_cli = true
log_cli_level = "INFO"
log_cli_date_format = "%Y-%m-%d-%H-%M-%S"
log_cli_format = "%(asctime)s - %(filename)s - %(module)s - %(funcName)s - %(lineno)d - %(levelname)s - %(message)s"
log_file = "./log/run.log"
log_file_level = "INFO"
log_file_date_format = "%Y-%m-%d-%H-%M-%S"
log_file_format = "%(asctime)s - %(filename)s - %(module)s - %(funcName)s - %(lineno)d - %(levelname)s - %(message)s"
testpaths = ["testsuite"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.ruff]
line-length = 88
target-version = "py311"
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".tox",
    "temp",
    "report",
]

[tool.ruff.lint]
select = ["E4", "E7", "E9", "F", "I", "UP", "B", "C4", "PIE", "SIM", "RET", "S"]
ignore = ["E203", "S101", "S603"]

[tool.ruff.lint.isort]
known-first-party = ["neox_test_common", "neox_test_win", "neox_test_scenarios"]
force-single-line = false
lines-after-imports = 2

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"

# MyPy configuration
[tool.mypy]
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
